#!/usr/bin/env python3

# Encrypted data from the binary (starting at 0x403040)
encrypted_data = bytes([
    0x33, 0x21, 0x00, 0x6d, 0x5f, 0xab, 0x86, 0xb4, 
    0xd4, 0x2d, 0x36, 0x3a, 0x4e, 0x90, 0x8c, 0xe3,
    0xcc, 0x2e, 0x09, 0x6c, 0x49, 0xb8, 0x8f, 0xf7, 
    0xcc, 0x22, 0x4e, 0x4d, 0x5e, 0xb8, 0x80, 0xcb,
    0xd3, 0xda, 0x20, 0x29, 0x70, 0x02, 0xb7, 0xd1, 
    0xb7, 0xc4
])

def decrypt():
    decrypted = bytearray(42)  # 0x2a = 42 bytes
    rcx = 0
    
    for i in range(42):
        # Decryption algorithm from the decompiled code:
        # rdx_2 = *(&data_403040 + i) ^ rcx;
        # rcx += 0x1f;
        # rdx_4 = rdx_2 ^ i >> 1 ^ 0x5a;
        # *(&data_405140 + i) = rdx_4;
        
        rdx_2 = encrypted_data[i] ^ (rcx & 0xFF)  # XOR with current rcx value
        rcx += 0x1f  # increment rcx by 0x1f
        rdx_4 = rdx_2 ^ (i >> 1) ^ 0x5a  # XOR with (i>>1) and 0x5a
        decrypted[i] = rdx_4 & 0xFF  # store as byte
    
    return decrypted

if __name__ == "__main__":
    result = decrypt()
    print("Decrypted data:")
    print(result)
    print("\nAs string:")
    try:
        print(result.decode('ascii', errors='ignore'))
    except:
        print("Could not decode as ASCII")
    
    print("\nAs hex:")
    print(result.hex())
