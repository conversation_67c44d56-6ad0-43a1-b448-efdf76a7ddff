int64_t _init(int64_t arg1 @ rax) __pure
{
    return arg1;
}

void sub_401010(int32_t arg1) __noreturn
{
    sub_401630();
    sub_4023f0();
    sub_401dc0(arg1);
    /* no return */
}

int64_t _INIT_1() __pure
{
    int32_t var_4 = 0;
    int32_t var_4_1 = 0x42;
    return 0x42;
}

int64_t _INIT_2()
{
    int64_t var_1020;
    int64_t var_1020_1 = var_1020;
    int32_t rcx = 0;
    void* fsbase;
    int64_t rax = *(fsbase + 0x28);
    int32_t rdx_4;
    
    for (int64_t i = 0; i != 0x2a; i += 1)
    {
        int32_t rdx_2 = *(&data_403040 + i) ^ rcx;
        rcx += 0x1f;
        rdx_4 = rdx_2 ^ i >> 1 ^ 0x5a;
        *(&data_405140 + i) = rdx_4;
    }
    
    data_40516a = 0;
    int32_t rax_2 = sub_401670("/proc/self/cmdline", 0, rdx_4);
    
    if (rax_2 >= 0)
    {
        char var_1038[0x18];
        int64_t rax_3 = sub_401a10(rax_2, &var_1038, 0xfff);
        sub_4019c0(rax_2);
        
        if (rax_3 > 0)
        {
            var_1038[rax_3] = 0;
            char* rax_4 = sub_401830(&var_1038, 0, rax_3);
            
            if (rax_4 && &rax_4[1] < &var_1038[rax_3])
            {
                if (!sub_401910(&rax_4[1], &data_405140))
                    sub_401770("Correct!");
                else
                    sub_401770("Wrong!");
                
                sub_401010(0);
                /* no return */
            }
        }
    }
    
    if (rax == *(fsbase + 0x28))
        return rax - *(fsbase + 0x28);
    
    sub_401610();
    /* no return */
}

int64_t sub_401170()
{
    sub_401770(&data_403023);
    return 0;
}

void _start() __noreturn
{
    sub_4011b0(&__return_addr);
    /* no return */
}

void sub_4011b0(int32_t* arg1) __noreturn
{
    /* tailcall */
    return sub_401570(sub_401170, *arg1, &arg1[2]);
}

void* deregister_tm_clones()
{
    return &data_405110;
}

int64_t sub_401210()
{
    return 0;
}

void _FINI_0()
{
    if (data_405120)
        return;
    
    deregister_tm_clones();
    data_405120 = 1;
}

int64_t _INIT_0()
{
    /* tailcall */
    return sub_401210();
}

int64_t sub_4012a0() __pure
{
    return;
}

int64_t sub_4012b0() __pure
{
    return;
}

int64_t sub_4012c0(int64_t* arg1, char* arg2)
{
    int64_t var_138[0x6];
    __builtin_memset(&var_138, 0, 0x130);
    data_405628 = arg1;
    void* rax_1;
    
    if (!*arg1)
        rax_1 = 8;
    else
    {
        int64_t rax = 0;
        int64_t rcx_1;
        
        do
        {
            rcx_1 = rax;
            rax += 1;
        } while (arg1[rax]);
        
        rax_1 = (rcx_1 << 3) + 0x10;
    }
    
    void* rax_2 = rax_1 + arg1;
    data_4051a8 = rax_2;
    int64_t i = *rax_2;
    void* rax_3 = rax_2 + 8;
    
    if (!i)
        data_405190 = 0;
    else
    {
        do
        {
            if (i <= 0x25)
                var_138[i] = *rax_3;
            
            i = *(rax_3 + 8);
            rax_3 += 0x10;
        } while (i);
        
        int64_t i_3;
        i = i_3;
        int64_t var_b8;
        data_405190 = var_b8;
        int64_t var_38;
        
        if (var_38)
            data_405178 = var_38;
    }
    
    data_4051d0 = i;
    
    if (arg2)
        goto label_401391;
    
    char* var_40;
    
    if (!var_40)
    {
        data_405180 = &data_40301c[6];
        data_405188 = &data_40301c[6];
    }
    else
    {
        arg2 = var_40;
    label_401391:
        data_405180 = arg2;
        void* rax_5 = &arg2[1];
        data_405188 = arg2;
        char i_1 = *arg2;
        
        while (i_1)
        {
            if (i_1 == 0x2f)
                data_405188 = rax_5;
            
            i_1 = *rax_5;
            rax_5 += 1;
        }
    }
    
    sub_401b60(&var_138);
    char* var_70;
    sub_4015b0(var_70);
    int64_t var_e0;
    int64_t var_d8;
    int64_t result;
    int64_t result_1;
    
    if (var_e0 == var_d8)
        result = result_1;
    int64_t var_d0;
    int64_t var_80;
    
    if (var_e0 != var_d8 || var_d0 != result || var_80)
    {
        int128_t var_158;
        __builtin_memset(&var_158, 0, 0x18);
        int64_t var_148;
        var_148 = 2;
        *var_158[8] = 1;
        result = syscall(sys_poll {7}, &var_158, 3, 0);
        
        if (result < 0)
            trap(0xd);
        
        int128_t* i_2 = &var_158;
        void var_140;
        
        do
        {
            if (*(i_2 + 6) & 0x20)
            {
                result = syscall(sys_open {2}, "/dev/null", 0x8002);
                
                if (result < 0)
                    trap(0xd);
            }
            
            i_2 += 8;
        } while (i_2 != &var_140);
        
        data_4051a2 = 1;
    }
    
    return result;
}

void sub_4014f0()
{
    for (void (** i)() = &init_array; i < &fini_array; i = &i[1])
        (*i)();
}

void sub_401570(int64_t arg1, int32_t arg2, int64_t* arg3) __noreturn
{
    int64_t rax = arg2;
    sub_4012c0(&arg3[rax + 1], *arg3);
    int64_t rax_1 = rax;
    int64_t r12;
    int64_t var_10_1 = r12;
    int64_t rbp;
    int64_t var_18_1 = rbp;
    int64_t rbx;
    int64_t var_20 = rbx;
    sub_4014f0();
    sub_401010(arg1(rax_1, arg3, &arg3[rax_1 + 1]));
    /* no return */
}

void* sub_4015b0(char* arg1)
{
    if (!arg1)
        data_405170 = 0x10868020d2ccb0;
    else
        sub_40194e(&data_405170, arg1, 8);
    
    (*(data_405170 + 1)) = 0;
    int64_t* fsbase;
    void* result = *fsbase;
    *(result + 0x28) = data_405170;
    return result;
}

void sub_401610() __noreturn
{
    trap(0xd);
}

int64_t sub_401620() __pure
{
    return;
}

int64_t sub_401630()
{
    void** const i = &data_404fd0;
    
    do
    {
        i -= 8;
        (*i)();
    } while (&fini_array < i);
    
    /* tailcall */
    return _fini(0);
}

int64_t sub_401670(int64_t arg1, int32_t arg2, int32_t arg3)
{
    void* fsbase;
    int64_t rax = *(fsbase + 0x28);
    uint64_t rcx_1;
    
    if (!(arg2 & 0x40))
        rcx_1 = 0;
    
    if (arg2 & 0x40 || !(~arg2 & 0x410000))
    {
        int32_t var_58_1 = 0x10;
        rcx_1 = arg3;
        void arg_8;
        void* var_50_1 = &arg_8;
        void var_38;
        void* var_48_1 = &var_38;
    }
    
    int32_t rdx;
    *rdx[1] = *arg2[1] | 0x80;
    int32_t rax_4 = sub_4019a0(2, arg1, rdx, rcx_1, 0, 0, 0);
    int64_t rdi = rax_4;
    
    if (rax_4 >= 0 && arg2 & 0x80000)
        syscall(sys_fcntl {0x48}, rdi, 2, 1);
    
    int64_t result = sub_401730(rdi);
    *(fsbase + 0x28);
    
    if (rax == *(fsbase + 0x28))
        return result;
    
    sub_401610();
    /* no return */
}

int64_t sub_401730(int64_t arg1)
{
    if (arg1 <= -0x1000)
        return arg1;
    
    *sub_401da0() = -(arg1);
    return -1;
}

uint64_t sub_401770(int64_t* arg1)
{
    int32_t rbx_1;
    int32_t r12;
    
    if (data_4050ac >= 0)
    {
        rbx_1 = -1;
        r12 = sub_401de0(&data_405020);
        
        if (sub_402110(arg1, &data_405020) >= 0)
        {
            if (data_4050b0 != 0xa)
            {
            label_4017a9:
                char* rax_2 = data_405048;
                
                if (rax_2 == data_405040)
                    goto label_4017f0;
                
                rbx_1 = 0;
                data_405048 = &rax_2[1];
                *rax_2 = 0xa;
            }
            else
            {
            label_4017f0:
                rbx_1 = sub_401f10(&data_405020, 0xa) >> 0x1f;
            }
        }
        
        if (r12)
            sub_401ec0(&data_405020);
    }
    else
    {
        rbx_1 = -1;
        r12 = 0;
        
        if (sub_402110(arg1, &data_405020) >= 0)
        {
            if (data_4050b0 == 0xa)
                goto label_4017f0;
            
            goto label_4017a9;
        }
    }
    return rbx_1;
}

char* sub_401830(char* arg1, char arg2, int64_t arg3)
{
    char* result_1 = arg1;
    int64_t i = arg3;
    uint32_t rsi = arg2;
    
    if (arg1 & 7)
    {
        while (true)
        {
            if (!i)
                return nullptr;
            
            if (*result_1 == rsi)
                break;
            
            result_1 = &result_1[1];
            i -= 1;
            
            if (!(result_1 & 7))
                goto label_401870;
        }
    }
    else
    {
    label_401870:
        
        if (!i)
            return nullptr;
    }
    
    if (*result_1 != rsi && i > 7)
    {
        do
        {
            int64_t rdx_3 = *result_1 ^ (rsi * 0x101010101010101);
            
            if (~rdx_3 & (rdx_3 - 0x101010101010101) & 0x8080808080808080)
                goto label_4018d3;
            
            i -= 8;
            result_1 = &result_1[8];
        } while (i > 7);
        
        if (!i)
            return nullptr;
    }
    
label_4018d3:
    char* result = result_1;
    
    while (true)
    {
        if (*result == rsi)
            return result;
        
        result = &result[1];
        int64_t i_1 = i;
        i -= 1;
        
        if (i_1 == 1)
            return nullptr;
    }
}

uint64_t sub_401910(char* arg1, char* arg2)
{
    char i = *arg1;
    uint32_t rcx = *arg2;
    int64_t rax = 1;
    
    if (i == rcx)
    {
        do
        {
            if (!i)
                return 0 - rcx;
            
            i = arg1[rax];
            rax += 1;
            rcx = arg2[rax - 1];
        } while (i == rcx);
    }
    
    return i - rcx;
}

char* sub_40194e(char* arg1, char* arg2, int64_t arg3)
{
    char* result = arg1;
    
    if (arg3 >= 8 && arg1 & 7)
    {
        do
        {
            *arg1 = *arg2;
            arg1 = &arg1[1];
            arg2 = &arg2[1];
            arg3 -= 1;
        } while (arg1 & 7);
    }
    
    char* rsi;
    char* rdi;
    rdi = __builtin_memcpy(arg1, arg2, arg3 >> 3 << 3);
    int32_t i_1 = arg3 & 7;
    
    if (i_1)
    {
        int32_t i;
        
        do
        {
            *rdi = *rsi;
            rdi = &rdi[1];
            rsi = &rsi[1];
            i = i_1;
            i_1 -= 1;
        } while (i != 1);
    }
    
    return result;
}

int64_t sub_401980(int64_t arg1, int64_t arg2, int64_t arg3, int64_t arg4, int64_t arg5, int64_t arg6, int64_t arg7)
{
    return syscall(arg1, arg2, arg3, arg4, arg5, arg6, arg7);
}

int64_t sub_4019a0(int64_t arg1, int64_t arg2, int64_t arg3, int64_t arg4, int64_t arg5, int64_t arg6, int64_t arg7)
{
    /* tailcall */
    return sub_401980(arg1, arg2, arg3, arg4, arg5, arg6, arg7);
}

uint64_t sub_4019b0(int32_t arg1) __pure
{
    return arg1;
}

int64_t sub_4019c0(int32_t arg1)
{
    int32_t rax_1 = sub_4019a0(3, sub_4019b0(arg1), 0, 0, 0, 0, 0);
    
    if (rax_1 == 0xfffffffc)
        rax_1 = 0;
    
    return sub_401730(rax_1);
}

int64_t sub_401a10(int32_t arg1, int64_t arg2, int64_t arg3)
{
    /* tailcall */
    return sub_401730(sub_4019a0(0, arg1, arg2, arg3, 0, 0, 0));
}

int64_t sub_401a40(int64_t* arg1)
{
    uint128_t zmm0 = arg1;
    zmm0 = _mm_unpacklo_epi64(zmm0, zmm0);
    *arg1 = arg1;
    int32_t rax = sub_40234d(arg1);
    
    if (rax < 0)
        return 0xffffffff;
    
    if (!rax)
        data_4051a0 = 1;
    
    arg1[7] = 2;
    arg1[6] = syscall(sys_set_tid_address {0xda}, &data_4057d0);
    arg1[0x15] = &data_4051d8;
    arg1[0x11] = &arg1[0x11];
    int64_t rax_3 = data_405178;
    *(arg1 + 0x10) = zmm0;
    arg1[4] = rax_3;
    return 0;
}

void* sub_401ad0(int64_t* arg1)
{
    void** i = data_4051b0;
    void* result = -(data_4051c0) & (arg1 + data_4051b8 - 0xc8);
    
    if (i)
    {
        void* rbp_1 = &arg1[1];
        
        do
        {
            rbp_1 += 8;
            *(rbp_1 - 8) = result - i[5];
            sub_40194e(result - i[5], i[1], i[2]);
            i = *i;
        } while (i);
    }
    
    *arg1 = data_4051c8;
    *(result + 8) = arg1;
    return result;
}

int64_t sub_401b60(void* arg1)
{
    int64_t rcx = *(arg1 + 0x28);
    int32_t* r10 = *(arg1 + 0x18);
    int64_t rax_3;
    int64_t rdx_2;
    void* rsi_3;
    
    if (!rcx)
    {
    label_401d78:
        rdx_2 = data_405658;
        rsi_3 = data_405648;
        rax_3 = data_405660;
    }
    else
    {
        int64_t rdi = *(arg1 + 0x20);
        int32_t* rax_1 = r10;
        void* rsi_1 = nullptr;
        int32_t* r8_1 = nullptr;
        
        while (true)
        {
            int32_t rdx_1 = *rax_1;
            
            if (rdx_1 != 6)
            {
                if (rdx_1 != 2)
                {
                    if (rdx_1 == 7)
                        r8_1 = rax_1;
                    else if (rdx_1 == 0x6474e551)
                    {
                        int64_t rdx_4 = *(rax_1 + 0x28);
                        
                        if (data_40510c < rdx_4)
                        {
                            if (rdx_4 > 0x800000)
                                rdx_4 = 0x800000;
                            
                            data_40510c = rdx_4;
                        }
                    }
                }
                
                rax_1 += rdi;
                int64_t temp2_1 = rcx;
                rcx -= 1;
                
                if (temp2_1 == 1)
                    break;
            }
            else
            {
                rsi_1 = r10 - *(rax_1 + 0x10);
                rax_1 += rdi;
                int64_t temp0_1 = rcx;
                rcx -= 1;
                
                if (temp0_1 == 1)
                    break;
            }
        }
        
        if (!r8_1)
            goto label_401d78;
        
        int64_t rax_2 = *(r8_1 + 0x20);
        rsi_3 = rsi_1 + *(r8_1 + 0x10);
        rdx_2 = *(r8_1 + 0x28);
        data_405648 = rsi_3;
        data_405650 = rax_2;
        rax_3 = *(r8_1 + 0x30);
        data_4051c8 = 1;
        data_405660 = rax_3;
        data_4051b0 = &data_405640;
    }
    
    int64_t rsi_7 = (-((rsi_3 + rdx_2)) & (rax_3 - 1)) + rdx_2;
    int64_t rdx_3 = rax_3 + 0xdf;
    data_405658 = rsi_7;
    data_405668 = rsi_7;
    
    if (rax_3 <= 7)
    {
        data_405660 = 8;
        rdx_3 = 0xe7;
        rax_3 = 8;
    }
    
    data_4051c0 = rax_3;
    void* rdi_1 = &data_405680;
    uint64_t rsi_9 = (rsi_7 + rdx_3) & 0xfffffffffffffff8;
    data_4051b8 = rsi_9;
    
    if (rsi_9 > 0x150)
        rdi_1 = syscall(sys_mmap {9}, nullptr, rsi_9, 3, 0x22, 0xffffffff, 0);
    
    void* rax_5 = sub_401ad0(rdi_1);
    *rax_5 = rax_5;
    uint128_t zmm0 = rax_5;
    zmm0 = _mm_unpacklo_epi64(zmm0, zmm0);
    int32_t rax_6 = sub_40234d(rax_5);
    
    if (rax_6 < 0)
        trap(0xd);
    
    if (!rax_6)
        data_4051a0 = 1;
    
    *(rax_5 + 0x38) = 2;
    *(rax_5 + 0x30) = syscall(sys_set_tid_address {0xda}, &data_4057d0);
    *(rax_5 + 0xa8) = &data_4051d8;
    *(rax_5 + 0x88) = rax_5 + 0x88;
    int64_t result = data_405178;
    *(rax_5 + 0x10) = zmm0;
    *(rax_5 + 0x20) = result;
    return result;
}

int64_t sub_401d6d(int64_t arg1) __pure
{
    return;
}

int64_t sub_401da0()
{
    int64_t* fsbase;
    return *fsbase + 0x34;
}

void sub_401dc0(int32_t arg1) __noreturn
{
    syscall(sys_exit_group {0xe7}, arg1);
    /* no return */
}

uint64_t sub_401de0(void* arg1)
{
    int64_t* fsbase;
    int32_t r8 = *(*fsbase + 0x30);
    int32_t rcx_1 = 0;
    
    if ((*(arg1 + 0x8c) & 0xbfffffff) != r8)
    {
        int32_t rax_2 = 0;
        
        if (0 == *(arg1 + 0x8c))
            *(arg1 + 0x8c) = r8;
        else
            rax_2 = *(arg1 + 0x8c);
        
        if (rax_2)
        {
            uint64_t r8_1 = r8 | 0x40000000;
            int32_t i_2 = 0;
            
            if (0 == *(arg1 + 0x8c))
                *(arg1 + 0x8c) = r8_1;
            else
                i_2 = *(arg1 + 0x8c);
            
            int32_t i_1 = i_2;
            
            if (i_2)
            {
                int32_t i;
                
                do
                {
                    int32_t rdx_3 = i_1 | 0x40000000;
                    int32_t i_3;
                    
                    if (!(i_1 & 0x40000000))
                    {
                        i_3 = i_1;
                        
                        if (i_3 == *(arg1 + 0x8c))
                            *(arg1 + 0x8c) = rdx_3;
                        else
                            i_3 = *(arg1 + 0x8c);
                    }
                    
                    if ((i_1 & 0x40000000 || i_3 == i_1) &&
                            syscall(sys_futex {0xca}, arg1 + 0x8c, 0x80, rdx_3, nullptr, r8_1, 0) == -0x26)
                        syscall(sys_futex {0xca}, arg1 + 0x8c, 0, rdx_3, nullptr, r8_1, 0);
                    
                    i = 0;
                    
                    if (0 == *(arg1 + 0x8c))
                        *(arg1 + 0x8c) = r8_1;
                    else
                        i = *(arg1 + 0x8c);
                    
                    i_1 = i;
                } while (i);
                return 1;
            }
        }
        
        rcx_1 = 1;
    }
    
    return rcx_1;
}

uint64_t sub_401ec0(void* arg1)
{
    int32_t result_1 = *(arg1 + 0x8c);
    *(arg1 + 0x8c) = 0;
    uint64_t result = result_1;
    
    if (result & 0x40000000)
    {
        int32_t entry_r9;
        struct timespec* entry_r10;
        result = syscall(sys_futex {0xca}, arg1 + 0x8c, 0x81, 1, entry_r10, 0xca, entry_r9);
        
        if (result == -0x26)
            return syscall(sys_futex {0xca}, arg1 + 0x8c, 1, 1, entry_r10, 0xca, entry_r9);
    }
    
    return result;
}

uint32_t sub_401f10(int32_t* arg1, char arg2)
{
    void* fsbase;
    int64_t rax = *(fsbase + 0x28);
    int64_t rax_1 = *(arg1 + 0x20);
    char result_1 = arg2;
    
    if (rax_1)
        goto label_401f3a;
    
    uint32_t result;
    
    if (sub_4020a0(arg1))
        result = -1;
    else
    {
        rax_1 = *(arg1 + 0x20);
    label_401f3a:
        char* rdx_1 = *(arg1 + 0x28);
        
        if (rdx_1 != rax_1)
            result = result_1;
        
        if (rdx_1 != rax_1 && result != arg1[0x24])
        {
            *(arg1 + 0x28) = &rdx_1[1];
            *rdx_1 = result;
        }
        else if ((*(arg1 + 0x48))(arg1, &result_1, 1) != 1)
            result = -1;
        else
            result = result_1;
    }
    
    *(fsbase + 0x28);
    
    if (rax == *(fsbase + 0x28))
        return result;
    
    sub_401610();
    /* no return */
}

uint64_t sub_401fc0(int32_t arg1) __pure
{
    return arg1;
}

int64_t sub_401fd0(void* arg1)
{
    return sub_401730(syscall(sys_close {3}, sub_4019b0(*(arg1 + 0x78))));
}

int64_t sub_402000(void* arg1, off_t arg2, int32_t arg3)
{
    /* tailcall */
    return sub_402360(*(arg1 + 0x78), arg2, arg3);
}

int64_t sub_402010(char* arg1, int64_t arg2, int64_t arg3)
{
    void* fsbase;
    int64_t rax = *(fsbase + 0x28);
    *(arg1 + 0x48) = sub_402440;
    void var_18;
    
    if (!(*arg1 & 0x40)
            && syscall(sys_ioctl {0x10}, *(arg1 + 0x78), 0x5413, &var_18, arg3, arg1, arg2))
        *(arg1 + 0x90) = 0xffffffff;
    
    int64_t result = sub_402440(arg1, arg2, arg3);
    *(fsbase + 0x28);
    
    if (rax == *(fsbase + 0x28))
        return result;
    
    sub_401610();
    /* no return */
}

int64_t sub_4020a0(int32_t* arg1)
{
    int32_t rdx = arg1[0x22];
    arg1[0x22] = (rdx - 1) | rdx;
    int32_t rax_2 = *arg1;
    
    if (rax_2 & 8)
    {
        *arg1 = rax_2 | 0x20;
        return 0xffffffff;
    }
    
    int64_t rax_3 = *(arg1 + 0x58);
    int64_t rcx = *(arg1 + 0x60);
    *(arg1 + 8) = {0};
    *(arg1 + 0x38) = rax_3;
    *(arg1 + 0x20) = _mm_unpacklo_epi64(rcx + rax_3, rax_3);
    return 0;
}

int64_t sub_402100()
{
    /* tailcall */
    return sub_4023f0();
}

uint64_t sub_402110(int64_t* arg1, int32_t* arg2)
{
    void* rax = sub_4022d0(arg1);
    uint64_t rax_1;
    rax_1 = sub_402230(arg1, 1, rax, arg2) != rax;
    return -(rax_1);
}

int64_t sub_402150(char* arg1, int64_t arg2, int32_t* arg3)
{
    char* rbp = arg1;
    int64_t rax = *(arg3 + 0x20);
    
    if (!rax)
    {
        if (sub_4020a0(arg3))
            return 0;
        
        rax = *(arg3 + 0x20);
    }
    
    char* rdi = *(arg3 + 0x28);
    
    if (rax - rdi < arg2)
        /* jump -> *(arg3 + 0x48) */
    
    int64_t rbx_1 = arg2;
    int64_t r14_1;
    
    if (arg3[0x24] >= 0)
    {
        while (true)
        {
            if (!rbx_1)
                goto label_40218e;
            
            if (rbp[rbx_1 - 1] == 0xa)
            {
                int64_t rax_3 = (*(arg3 + 0x48))(arg3, rbp, rbx_1);
                
                if (rax_3 < rbx_1)
                    return rax_3;
                
                rdi = *(arg3 + 0x28);
                rbp = &rbp[rbx_1];
                r14_1 = arg2 - rbx_1;
                break;
            }
            
            rbx_1 -= 1;
        }
    }
    else
    {
    label_40218e:
        r14_1 = arg2;
    }
    
    sub_40194e(rdi, rbp, r14_1);
    *(arg3 + 0x28) += r14_1;
    return arg2;
}

uint64_t sub_402230(char* arg1, int64_t arg2, uint64_t arg3, int32_t* arg4)
{
    uint64_t result = 0;
    int64_t r14_1 = arg2 * arg3;
    
    if (arg2)
        result = arg3;
    
    int64_t r13_1;
    
    if (arg4[0x23] >= 0)
    {
        int32_t rax_5 = sub_401de0(arg4);
        r13_1 = sub_402150(arg1, r14_1, arg4);
        
        if (rax_5)
            sub_401ec0(arg4);
    }
    else
        r13_1 = sub_402150(arg1, r14_1, arg4);
    
    if (r14_1 == r13_1)
        return result;
    
    return COMBINE(0, r13_1) / arg2;
}

void* sub_4022d0(int64_t* arg1)
{
    int64_t* rax = arg1;
    
    if (arg1 & 7)
    {
        do
        {
            if (!*rax)
                return rax - arg1;
            
            rax += 1;
        } while (rax & 7);
    }
    
    int64_t rdx = *rax;
    
    if (!(~rdx & (rdx - 0x101010101010101) & 0x8080808080808080))
    {
        int64_t rdx_3;
        
        do
        {
            rdx_3 = rax[1];
            rax = &rax[1];
        } while (!(~rdx_3 & (rdx_3 - 0x101010101010101) & 0x8080808080808080));
    }
    
    while (*rax)
        rax += 1;
    
    return rax - arg1;
}

int64_t sub_40234d(uint64_t arg1)
{
    return syscall(sys_arch_prctl {0x9e}, 0x1002, arg1);
}

int64_t sub_402360(int32_t arg1, off_t arg2, int32_t arg3)
{
    /* tailcall */
    return sub_401730(syscall(sys_lseek {8}, arg1, arg2, arg3));
}

void sub_402380(void* arg1)
{
    if (!arg1)
        return;
    
    if (*(arg1 + 0x8c) >= 0)
        sub_401de0(arg1);
    
    if (*(arg1 + 0x28) != *(arg1 + 0x38))
        (*(arg1 + 0x48))(arg1, 0, 0);
    
    if (*(arg1 + 8) == *(arg1 + 0x10))
        return;
    
    /* jump -> *(arg1 + 0x50) */
}

int64_t sub_4023f0()
{
    sub_402570();
    
    for (void* i = data_4057e8; i; i = *(i + 0x70))
        sub_402380(i);
    
    sub_402380(data_4057d8);
    sub_402380(data_405008);
    /* tailcall */
    return sub_402380(data_4057d8);
}

int64_t sub_402440(int32_t* arg1, int64_t arg2, int64_t arg3)
{
    int32_t rbp = 2;
    int32_t rbx = 2;
    int64_t var_68;
    int64_t* r13 = &var_68;
    void* fsbase;
    int64_t rax = *(fsbase + 0x28);
    int64_t rax_1 = *(arg1 + 0x38);
    int64_t var_58 = arg2;
    int64_t var_50 = arg3;
    int64_t r14_1 = *(arg1 + 0x28) - rax_1;
    var_68 = rax_1;
    int64_t var_60 = r14_1;
    int64_t r14_2 = r14_1 + arg3;
    int64_t result;
    
    while (true)
    {
        int64_t rax_2 = sub_401730(syscall(sys_writev {0x14}, arg1[0x1e], r13, rbx));
        
        if (r14_2 == rax_2)
        {
            int64_t rax_4 = *(arg1 + 0x58);
            int64_t rcx_2 = *(arg1 + 0x60) + rax_4;
            *(arg1 + 0x38) = rax_4;
            result = arg3;
            *(arg1 + 0x20) = _mm_unpacklo_epi64(rcx_2, rax_4);
            break;
        }
        
        if (rax_2 < 0)
        {
            *arg1 |= 0x20;
            result = 0;
            *(arg1 + 0x38) = 0;
            *(arg1 + 0x20) = {0};
            
            if (rbp != 2)
                result = arg3 - r13[1];
            
            break;
        }
        
        int64_t rdx = r13[1];
        r14_2 -= rax_2;
        
        if (rdx < rax_2)
        {
            rbp -= 1;
            rax_2 -= rdx;
            r13 = &r13[2];
            rdx = r13[1];
            rbx = rbp;
        }
        
        *r13 += rax_2;
        r13[1] = rdx - rax_2;
    }
    
    *(fsbase + 0x28);
    
    if (rax == *(fsbase + 0x28))
        return result;
    
    sub_401610();
    /* no return */
}

int64_t sub_402570()
{
    sub_4025a0(&data_4057e0);
    return &data_4057e8;
}

int64_t __sanitizer::GetPwd()
{
    /* tailcall */
    return sub_402690(&data_4057e0);
}

void sub_4025a0(int32_t* arg1)
{
    int32_t rcx = data_4051a3;
    
    if (!rcx)
        return;
    
    int32_t rax = 0;
    
    if (0 == *arg1)
        *arg1 = 0x80000001;
    else
        rax = *arg1;
    
    int32_t rdx_1 = rax;
    
    if (rcx < 0)
    {
        data_4051a3 = 0;
        
        if (!rax)
            return;
        
        goto label_4025c4;
    }
    
    if (!rax)
        return;
    
label_4025c4:
    int32_t r8_1 = 0xa;
    
    while (true)
    {
        int32_t rcx_1;
        int32_t rsi_1;
        
        if (rdx_1 >= 0)
        {
            rsi_1 = rdx_1 - 0x7fffffff;
            rcx_1 = rdx_1;
        }
        else
        {
            rcx_1 = rdx_1 + 0x7fffffff;
            rsi_1 = rdx_1;
        }
        
        rax = rcx_1;
        
        if (rax == *arg1)
            *arg1 = rsi_1;
        else
            rax = *arg1;
        
        rdx_1 = rax;
        
        if (rcx_1 == rax)
            break;
        
        int32_t temp1_1 = r8_1;
        r8_1 -= 1;
        
        if (temp1_1 == 1)
        {
            int32_t r8_2 = *arg1;
            *arg1 += 1;
            int32_t* r8_3 = r8_2 + 1;
            int32_t rax_1;
            int32_t rdx_2;
            
            do
            {
                rdx_2 = r8_3;
                
                if (r8_3 < 0)
                {
                    int32_t rdx_3 = r8_3;
                    
                    if (syscall(sys_futex {0xca}, arg1, 0x80, rdx_3, nullptr, r8_3, 0xca) == -0x26)
                        syscall(sys_futex {0xca}, arg1, 0, rdx_3, nullptr, r8_3, 0xca);
                    
                    rdx_2 = r8_3 + 0x7fffffff;
                }
                
                rax_1 = rdx_2;
                
                if (rax_1 == *arg1)
                    *arg1 = rdx_2 - 0x80000000;
                else
                    rax_1 = *arg1;
                
                r8_3 = rax_1;
            } while (rdx_2 != rax_1);
            return;
        }
    }
}

uint64_t sub_402690(int32_t* arg1)
{
    uint64_t result = *arg1;
    
    if (result < 0)
    {
        result = *arg1;
        *arg1 += 0x7fffffff;
        
        if (result != 0x80000001)
        {
            int32_t entry_r9;
            struct timespec* entry_r10;
            result = syscall(sys_futex {0xca}, arg1, 0x81, 1, entry_r10, 0xca, entry_r9);
            
            if (result == -0x26)
                return syscall(sys_futex {0xca}, arg1, 1, 1, entry_r10, 0xca, entry_r9);
        }
    }
    
    return result;
}

int64_t _fini(int64_t arg1 @ rax) __pure
{
    return arg1;
}

